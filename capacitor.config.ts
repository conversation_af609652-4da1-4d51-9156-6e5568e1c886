import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.santillana.agendafamiliar',
  appName: 'Agenda Familiar',
  webDir: 'dist',
  server: {
    androidScheme: 'https'
  },
  plugins: {
    InAppBrowser: {
      // Configure InAppBrowser for OAuth2 authentication
      presentationStyle: 'popover',
      toolbarColor: '#ffffff',
      showTitle: false,
      enableViewportScale: false
    },
    App: {
      // Handle deep links for OAuth2 callbacks
      deepLinkingEnabled: true,
      // Register custom URL scheme for OAuth2 callbacks
      urlScheme: 'sumun'
    },
    CapacitorCookies: {
      // Enable native cookie management for session isolation
      enabled: true
    },
    CapacitorHttp: {
      // Enable native HTTP for better performance and CORS handling
      enabled: true
    }
  }
};

export default config;
