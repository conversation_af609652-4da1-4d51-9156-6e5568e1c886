import React, { useState } from 'react';
import {
  IonContent,
  IonPage,
  IonCard,
  IonCardContent,
  IonButton,
  IonIcon,
  IonAlert,
} from '@ionic/react';
import {
  logInOutline,
  alertCircleOutline
} from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import { ROUTES } from '../../routes/routes';
import LoadingButton from '../../components/LoadingButton/LoadingButton';
import { debugLog } from '../../config/environment.config';
import { useCapacitorUser } from '../../contexts/CapacitorUserContext';
import { CapacitorAuthService } from '../../services/capacitor-auth.service';
// Removed AuthPage.css - using default Ionic styling

const CapacitorAuthPage: React.FC = () => {
  const history = useHistory();
  const { updateCapacitorAuthState } = useCapacitorUser();
  const [isLoading, setIsLoading] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertType, setAlertType] = useState<'success' | 'error'>('error');

  const handleLogin = async () => {
    if (isLoading) return;

    setIsLoading(true);
    setShowAlert(false);

    try {
      debugLog('CapacitorAuthPage - Starting Capacitor-native authentication');
      console.log('📱 [UI] Starting Capacitor-native authentication with InAppBrowser');

      // Use streamlined Capacitor authentication service
      const authResult = await CapacitorAuthService.signIn();

      console.log('🎉 [UI] Authentication successful:', {
        userId: authResult.profile.sub,
        userName: authResult.profile.name,
        userEmail: authResult.profile.email,
        expiresIn: authResult.expiresIn
      });

      debugLog('CapacitorAuthPage - Authentication successful', {
        userId: authResult.profile.sub,
        userName: authResult.profile.name
      });

      // Update user context with authentication result
      if (updateCapacitorAuthState) {
        await updateCapacitorAuthState(authResult);
      }

      // Navigate to home page
      history.replace(ROUTES.HOME);

    } catch (error: any) {
      console.error('❌ [UI] Authentication failed:', error);
      debugLog('CapacitorAuthPage - Authentication failed', error);

      // Show user-friendly error message
      const errorMessage = error?.message || 'Error de autenticación. Por favor, inténtalo de nuevo.';
      setAlertMessage(errorMessage);
      setAlertType('error');
      setShowAlert(true);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <IonPage>
      <IonContent className="auth-content">
        {/* Main Auth Card */}
        <div className="auth-container">
          <IonCard className="auth-card">
            <IonCardContent className="auth-card-content">
              {/* Header */}
              <div className="auth-header">
                <div className="auth-icon-container">
                  <IonIcon
                    icon={logInOutline}
                    className="auth-main-icon"
                    aria-hidden="true"
                  />
                </div>
                <h1 className="auth-title">
                  Bienvenido a Agenda Familiar
                </h1>
                <p className="auth-subtitle">
                  Inicia sesión con tu cuenta de Santillana Connect para acceder a la aplicación.
                </p>
              </div>

              {/* Login Button */}
              <div className="auth-form">
                <LoadingButton
                  expand="block"
                  className="auth-submit-button"
                  loading={isLoading}
                  loadingText="Iniciando sesión..."
                  icon={logInOutline}
                  iconSlot="start"
                  onClick={handleLogin}
                  disabled={isLoading}
                  aria-label="Iniciar sesión con Santillana Connect"
                >
                  Iniciar Sesión con Santillana Connect
                </LoadingButton>
              </div>

              {/* Info Section */}
              <div className="auth-info">
                <p className="auth-info-text">
                  Al iniciar sesión, serás redirigido de forma segura a Santillana Connect 
                  para autenticarte con tus credenciales.
                </p>
              </div>
            </IonCardContent>
          </IonCard>
        </div>

        {/* Error Alert */}
        <IonAlert
          isOpen={showAlert}
          onDidDismiss={() => setShowAlert(false)}
          header={alertType === 'error' ? 'Error de Autenticación' : 'Éxito'}
          message={alertMessage}
          buttons={[
            {
              text: 'Entendido',
              role: 'cancel',
              handler: () => {
                setShowAlert(false);
              }
            },
            ...(alertType === 'error' ? [{
              text: 'Reintentar',
              handler: () => {
                setShowAlert(false);
                handleLogin();
              }
            }] : [])
          ]}
        />
      </IonContent>
    </IonPage>
  );
};

export default CapacitorAuthPage;
