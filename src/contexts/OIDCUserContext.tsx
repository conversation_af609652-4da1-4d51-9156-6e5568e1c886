import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react';
import { OIDCCapacitorService, OIDCAuthResult, OIDCUserInfo } from '../services/oidc-capacitor.service';
import { UserApiService } from '../services/user-api.service';
import { debugLog } from '../config/environment.config';

/**
 * OpenID Connect User Context for Capacitor
 * Provides seamless authentication with Santillana Connect using OIDC standard
 */

export interface Student {
  id: string;
  name: string;
  grade: string;
  school: string;
  avatar?: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  role: string;
  avatar: string;
  memberSince: string;
  children: Student[];
  // OIDC specific fields
  oidcProfile: OIDCUserInfo;
  accessToken: string;
}

interface OIDCUserContextType {
  user: User | null;
  isAuthenticated: boolean;
  selectedStudent: Student | null;
  isLoading: boolean;
  // Authentication methods
  login: () => Promise<void>;
  logout: () => Promise<void>;
  // User management
  selectStudent: (student: Student) => void;
  updateUser: (userData: Partial<User>) => void;
  // Token management
  getAccessToken: () => Promise<string | null>;
  refreshTokens: () => Promise<boolean>;
}

const OIDCUserContext = createContext<OIDCUserContextType | undefined>(undefined);

const SELECTED_STUDENT_KEY = 'oidc_selected_student';

interface OIDCUserProviderProps {
  children: ReactNode;
}

export const OIDCUserProvider: React.FC<OIDCUserProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);

  /**
   * Create User object from OIDC authentication result
   */
  const createUserFromOIDC = useCallback(async (authResult: OIDCAuthResult): Promise<User> => {
    const { userInfo, tokens } = authResult;

    try {
      // Fetch additional user data from Santillana APIs
      const userData = await UserApiService.fetchUserProfile(userInfo, tokens.access_token);
      
      // Merge OIDC profile with user data
      const user: User = {
        ...userData,
        oidcProfile: userInfo,
        accessToken: tokens.access_token
      };

      debugLog('OIDCUserContext - User created from OIDC result', {
        userId: user.id,
        email: user.email,
        name: user.name,
        childrenCount: user.children.length
      });

      return user;
    } catch (error) {
      debugLog('OIDCUserContext - Failed to fetch user data, using OIDC profile only', error);
      
      // Fallback to OIDC profile data only
      const fallbackUser: User = {
        id: userInfo.sub,
        name: userInfo.name || userInfo.preferred_username || 'Usuario',
        email: userInfo.email,
        phone: undefined,
        role: 'parent',
        avatar: userInfo.picture || '/default-avatar.png',
        memberSince: new Date().toISOString(),
        children: [], // Will be populated later when API is available
        oidcProfile: userInfo,
        accessToken: tokens.access_token
      };

      return fallbackUser;
    }
  }, []);

  /**
   * Load selected student from storage
   */
  const loadSelectedStudent = useCallback(async (user: User): Promise<Student | null> => {
    if (user.children.length === 0) {
      return null;
    }

    try {
      const stored = localStorage.getItem(SELECTED_STUDENT_KEY);
      if (stored) {
        const studentId = JSON.parse(stored);
        const student = user.children.find(child => child.id === studentId);
        if (student) {
          debugLog('OIDCUserContext - Loaded selected student from storage', { studentName: student.name });
          return student;
        }
      }
    } catch (error) {
      debugLog('OIDCUserContext - Failed to load selected student from storage', error);
    }

    // Default to first child
    const defaultStudent = user.children[0];
    debugLog('OIDCUserContext - Using default student (first child)', { studentName: defaultStudent.name });
    return defaultStudent;
  }, []);

  /**
   * Initialize authentication state on app start
   */
  const initializeAuth = useCallback(async () => {
    if (isInitialized) return;

    debugLog('OIDCUserContext - Initializing OIDC authentication state');
    setIsLoading(true);

    try {
      // Check for existing authentication
      const existingAuth = await OIDCCapacitorService.getCurrentAuth();
      
      if (existingAuth) {
        debugLog('OIDCUserContext - Found existing OIDC authentication');
        
        // Create user from existing auth
        const userData = await createUserFromOIDC(existingAuth);
        setUser(userData);
        setIsAuthenticated(true);

        // Load selected student
        const student = await loadSelectedStudent(userData);
        setSelectedStudent(student);

        debugLog('OIDCUserContext - OIDC authentication restored successfully', {
          userName: userData.name,
          userEmail: userData.email
        });
      } else {
        debugLog('OIDCUserContext - No existing OIDC authentication found');
        setUser(null);
        setIsAuthenticated(false);
        setSelectedStudent(null);
      }
    } catch (error) {
      console.error('❌ OIDCUserContext - Error initializing auth:', error);
      setUser(null);
      setIsAuthenticated(false);
      setSelectedStudent(null);
    } finally {
      setIsLoading(false);
      setIsInitialized(true);
    }
  }, [isInitialized, createUserFromOIDC, loadSelectedStudent]);

  // Initialize on mount
  useEffect(() => {
    initializeAuth();
  }, [initializeAuth]);

  /**
   * Login using OpenID Connect
   */
  const login = useCallback(async () => {
    try {
      debugLog('OIDCUserContext - Starting OIDC login');
      setIsLoading(true);

      const authResult = await OIDCCapacitorService.authenticate();
      
      // Create user from auth result
      const userData = await createUserFromOIDC(authResult);
      setUser(userData);
      setIsAuthenticated(true);

      // Load selected student
      const student = await loadSelectedStudent(userData);
      setSelectedStudent(student);

      debugLog('OIDCUserContext - OIDC login completed successfully', {
        userName: userData.name,
        userEmail: userData.email,
        isAuthenticated: true
      });

    } catch (error) {
      console.error('❌ OIDCUserContext - Login failed:', error);
      setUser(null);
      setIsAuthenticated(false);
      setSelectedStudent(null);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [createUserFromOIDC, loadSelectedStudent]);

  /**
   * Logout and clear session
   */
  const logout = useCallback(async () => {
    try {
      debugLog('OIDCUserContext - Starting OIDC logout');
      
      await OIDCCapacitorService.signOut();
      
      setUser(null);
      setSelectedStudent(null);
      setIsAuthenticated(false);
      localStorage.removeItem(SELECTED_STUDENT_KEY);
      
      debugLog('OIDCUserContext - OIDC logout completed');
    } catch (error) {
      console.error('❌ OIDCUserContext - Error during logout:', error);
      throw error;
    }
  }, []);

  /**
   * Select a student
   */
  const selectStudent = useCallback((student: Student) => {
    setSelectedStudent(student);
    localStorage.setItem(SELECTED_STUDENT_KEY, JSON.stringify(student.id));
    debugLog('OIDCUserContext - Student selected', { studentName: student.name });
  }, []);

  /**
   * Update user data
   */
  const updateUser = useCallback((userData: Partial<User>) => {
    setUser(prevUser => {
      if (!prevUser) return null;
      const updatedUser = { ...prevUser, ...userData };
      debugLog('OIDCUserContext - User updated', { userName: updatedUser.name });
      return updatedUser;
    });
  }, []);

  /**
   * Get current access token (with automatic refresh if needed)
   */
  const getAccessToken = useCallback(async (): Promise<string | null> => {
    try {
      const currentAuth = await OIDCCapacitorService.getCurrentAuth();
      
      if (!currentAuth) {
        debugLog('OIDCUserContext - No current authentication for token');
        return null;
      }

      // Check if token is close to expiring (refresh if less than 5 minutes left)
      const expiresIn = currentAuth.tokens.expires_in;
      if (expiresIn < 300) { // 5 minutes
        debugLog('OIDCUserContext - Token close to expiring, attempting refresh');
        const refreshed = await OIDCCapacitorService.refreshToken();
        
        if (refreshed) {
          // Update user with new token
          if (user) {
            setUser(prevUser => prevUser ? { ...prevUser, accessToken: refreshed.access_token } : null);
          }
          return refreshed.access_token;
        } else {
          debugLog('OIDCUserContext - Token refresh failed, user needs to re-authenticate');
          await logout();
          return null;
        }
      }

      return currentAuth.tokens.access_token;
    } catch (error) {
      console.error('❌ OIDCUserContext - Error getting access token:', error);
      return null;
    }
  }, [user, logout]);

  /**
   * Manually refresh tokens
   */
  const refreshTokens = useCallback(async (): Promise<boolean> => {
    try {
      debugLog('OIDCUserContext - Manually refreshing tokens');
      
      const refreshed = await OIDCCapacitorService.refreshToken();
      
      if (refreshed && user) {
        setUser(prevUser => prevUser ? { ...prevUser, accessToken: refreshed.access_token } : null);
        debugLog('OIDCUserContext - Tokens refreshed successfully');
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('❌ OIDCUserContext - Error refreshing tokens:', error);
      return false;
    }
  }, [user]);

  const value: OIDCUserContextType = {
    user,
    isAuthenticated,
    selectedStudent,
    isLoading,
    login,
    logout,
    selectStudent,
    updateUser,
    getAccessToken,
    refreshTokens
  };

  return (
    <OIDCUserContext.Provider value={value}>
      {children}
    </OIDCUserContext.Provider>
  );
};

export const useOIDCUser = (): OIDCUserContextType => {
  const context = useContext(OIDCUserContext);
  if (context === undefined) {
    throw new Error('useOIDCUser must be used within an OIDCUserProvider');
  }
  return context;
};

// Export for compatibility
export const useUser = useOIDCUser;
