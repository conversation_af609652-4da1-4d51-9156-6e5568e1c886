import React, { useState } from 'react';
import { IonButton, IonIcon, IonSpinner, IonText } from '@ionic/react';
import { logIn, shield } from 'ionicons/icons';
import { useOIDCUser } from '../contexts/OIDCUserContext';
import { debugLog } from '../config/environment.config';

/**
 * OpenID Connect Login Button Component
 * Provides seamless authentication with Santillana Connect
 */

interface OIDCLoginButtonProps {
  className?: string;
  size?: 'small' | 'default' | 'large';
  fill?: 'clear' | 'outline' | 'solid';
  expand?: 'block' | 'full';
  disabled?: boolean;
  onLoginStart?: () => void;
  onLoginSuccess?: () => void;
  onLoginError?: (error: Error) => void;
}

export const OIDCLoginButton: React.FC<OIDCLoginButtonProps> = ({
  className = '',
  size = 'default',
  fill = 'solid',
  expand,
  disabled = false,
  onLoginStart,
  onLoginSuccess,
  onLoginError
}) => {
  const { login, isLoading } = useOIDCUser();
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleLogin = async () => {
    if (isAuthenticating || isLoading || disabled) {
      return;
    }

    try {
      debugLog('OIDCLoginButton - Starting authentication');
      setIsAuthenticating(true);
      setError(null);
      
      // Notify parent component
      onLoginStart?.();

      // Perform OIDC authentication
      await login();

      debugLog('OIDCLoginButton - Authentication successful');
      onLoginSuccess?.();

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Authentication failed';
      console.error('❌ OIDCLoginButton - Authentication failed:', error);
      
      setError(errorMessage);
      onLoginError?.(error instanceof Error ? error : new Error(errorMessage));
    } finally {
      setIsAuthenticating(false);
    }
  };

  const isButtonDisabled = disabled || isLoading || isAuthenticating;
  const showSpinner = isLoading || isAuthenticating;

  return (
    <div className={`oidc-login-container ${className}`}>
      <IonButton
        onClick={handleLogin}
        disabled={isButtonDisabled}
        size={size}
        fill={fill}
        expand={expand}
        className="oidc-login-button"
      >
        {showSpinner ? (
          <>
            <IonSpinner name="crescent" className="mr-2" />
            <span>Autenticando...</span>
          </>
        ) : (
          <>
            <IonIcon icon={shield} className="mr-2" />
            <span>Iniciar Sesión con Santillana Connect</span>
          </>
        )}
      </IonButton>

      {error && (
        <IonText color="danger" className="mt-2 block text-sm">
          <p>❌ {error}</p>
          <p className="text-xs mt-1">
            Por favor, inténtalo de nuevo o contacta con soporte si el problema persiste.
          </p>
        </IonText>
      )}
    </div>
  );
};

/**
 * Simplified OIDC Login Button for quick integration
 */
export const SimpleOIDCLogin: React.FC<{ className?: string }> = ({ className }) => {
  return (
    <OIDCLoginButton
      className={className}
      expand="block"
      size="large"
      fill="solid"
    />
  );
};

/**
 * OIDC Login Card Component
 * Complete login interface with branding and instructions
 */
export const OIDCLoginCard: React.FC<{ className?: string }> = ({ className = '' }) => {
  return (
    <div className={`oidc-login-card bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 ${className}`}>
      {/* Header */}
      <div className="text-center mb-6">
        <div className="flex justify-center mb-4">
          <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
            <IonIcon icon={shield} className="text-3xl text-blue-600 dark:text-blue-400" />
          </div>
        </div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Bienvenido a Agenda Familiar
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Inicia sesión con tu cuenta de Santillana Connect para acceder a toda la información de tus hijos.
        </p>
      </div>

      {/* Login Button */}
      <div className="mb-4">
        <SimpleOIDCLogin />
      </div>

      {/* Security Notice */}
      <div className="text-center">
        <IonText color="medium" className="text-xs">
          <p>
            🔒 Tu información está protegida con los más altos estándares de seguridad.
            Al iniciar sesión, aceptas nuestros términos de uso y política de privacidad.
          </p>
        </IonText>
      </div>
    </div>
  );
};

/**
 * OIDC Status Indicator
 * Shows current authentication status
 */
export const OIDCStatusIndicator: React.FC = () => {
  const { isAuthenticated, user, isLoading } = useOIDCUser();

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2 text-gray-500">
        <IonSpinner name="dots" />
        <span className="text-sm">Verificando sesión...</span>
      </div>
    );
  }

  if (isAuthenticated && user) {
    return (
      <div className="flex items-center space-x-2 text-green-600">
        <IonIcon icon={shield} />
        <span className="text-sm">Conectado como {user.name}</span>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2 text-gray-500">
      <IonIcon icon={logIn} />
      <span className="text-sm">No autenticado</span>
    </div>
  );
};

export default OIDCLoginButton;
