/**
 * Crypto Utilities
 * Consolidated cryptographic functionality
 * Combines functionality from crypto-fallback.ts
 */

/**
 * Cryptographic utilities with fallback implementations
 */
export class CryptoUtils {
  /**
   * Generate SHA256 hash of input string
   */
  static async sha256(input: string): Promise<ArrayBuffer> {
    const encoder = new TextEncoder();
    const data = encoder.encode(input);

    if (typeof crypto !== 'undefined' && crypto.subtle) {
      // Use Web Crypto API if available
      return await crypto.subtle.digest('SHA-256', data);
    } else {
      // Fallback implementation
      return Promise.resolve(this.sha256Fallback(input));
    }
  }

  /**
   * Base64 URL encode (RFC 4648 Section 5)
   */
  static base64URLEncode(buffer: ArrayBuffer | string): string {
    let base64: string;
    
    if (typeof buffer === 'string') {
      base64 = btoa(buffer);
    } else {
      const bytes = new Uint8Array(buffer);
      const binary = Array.from(bytes, byte => String.fromCharCode(byte)).join('');
      base64 = btoa(binary);
    }
    
    return base64
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  /**
   * Base64 URL decode
   */
  static base64URLDecode(input: string): ArrayBuffer {
    // Add padding if needed
    let padded = input;
    while (padded.length % 4) {
      padded += '=';
    }
    
    // Replace URL-safe characters
    const base64 = padded.replace(/-/g, '+').replace(/_/g, '/');
    
    // Decode
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);
    
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    
    return bytes.buffer;
  }

  /**
   * Generate random string for PKCE code verifier
   */
  static generateCodeVerifier(length: number = 128): string {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
    let result = '';
    
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
      const values = new Uint8Array(length);
      crypto.getRandomValues(values);
      
      for (let i = 0; i < length; i++) {
        result += charset[values[i] % charset.length];
      }
    } else {
      // Fallback using Math.random
      for (let i = 0; i < length; i++) {
        result += charset[Math.floor(Math.random() * charset.length)];
      }
    }
    
    return result;
  }

  /**
   * Generate PKCE code challenge from verifier
   */
  static async generateCodeChallenge(verifier: string): Promise<string> {
    const hash = await this.sha256(verifier);
    return this.base64URLEncode(hash);
  }

  /**
   * Generate random state parameter
   */
  static generateState(length: number = 32): string {
    return this.generateCodeVerifier(length);
  }

  /**
   * Generate random nonce
   */
  static generateNonce(length: number = 32): string {
    return this.generateCodeVerifier(length);
  }

  /**
   * Fallback SHA256 implementation (simple hash)
   * Note: This is not cryptographically secure and should only be used as a last resort
   */
  private static sha256Fallback(input: string): ArrayBuffer {
    console.warn('Using fallback SHA256 implementation - not cryptographically secure');
    
    // Simple hash function (not secure, but functional for development)
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    // Convert to ArrayBuffer
    const buffer = new ArrayBuffer(4);
    const view = new DataView(buffer);
    view.setUint32(0, hash, false);
    
    return buffer;
  }

  /**
   * Validate that crypto operations are available
   */
  static isSecureCryptoAvailable(): boolean {
    return typeof crypto !== 'undefined' && 
           typeof crypto.subtle !== 'undefined' && 
           typeof crypto.getRandomValues !== 'undefined';
  }

  /**
   * Get crypto implementation info
   */
  static getCryptoInfo(): { secure: boolean; webCrypto: boolean; randomValues: boolean } {
    return {
      secure: this.isSecureCryptoAvailable(),
      webCrypto: typeof crypto !== 'undefined' && typeof crypto.subtle !== 'undefined',
      randomValues: typeof crypto !== 'undefined' && typeof crypto.getRandomValues !== 'undefined'
    };
  }
}

// Legacy exports for backward compatibility
export const CryptoFallback = CryptoUtils;
