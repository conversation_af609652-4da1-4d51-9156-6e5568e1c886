/**
 * Test suite for Smart Authentication Flow
 * Tests the new seamless authentication functionality
 */

import { AuthService } from '../services/auth.service';

// Mock Capacitor modules for testing
jest.mock('@capacitor/core', () => ({
  Capacitor: {
    isNativePlatform: () => true,
    getPlatform: () => 'ios'
  },
  CapacitorCookies: {
    clearAllCookies: jest.fn()
  }
}));

jest.mock('@capacitor/inappbrowser', () => ({
  InAppBrowser: {
    openInSystemBrowser: jest.fn(),
    close: jest.fn()
  },
  DefaultSystemBrowserOptions: {}
}));

jest.mock('@capacitor/app', () => ({
  App: {
    addListener: jest.fn()
  }
}));

jest.mock('@capacitor/preferences', () => ({
  Preferences: {
    get: jest.fn(),
    set: jest.fn(),
    remove: jest.fn(),
    clear: jest.fn()
  }
}));

describe('Smart Authentication Flow', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('signIn() method', () => {
    it('should use smart authentication flow by default', async () => {
      // Mock getCurrentUser to return null (no existing session)
      const getCurrentUserSpy = jest.spyOn(AuthService as any, 'getCurrentUser')
        .mockResolvedValue(null);

      // Mock signInForced to avoid actual authentication
      const signInForcedSpy = jest.spyOn(AuthService, 'signInForced')
        .mockResolvedValue({
          accessToken: 'test-token',
          idToken: 'test-id-token',
          expiresIn: 3600,
          profile: {
            sub: 'test-user',
            name: 'Test User',
            email: '<EMAIL>'
          }
        });

      // Call signIn (which should now use smart flow)
      const result = await AuthService.signIn();

      // Verify it checked for existing user first
      expect(getCurrentUserSpy).toHaveBeenCalled();
      
      // Verify it fell back to forced authentication when no session found
      expect(signInForcedSpy).toHaveBeenCalled();
      
      // Verify result
      expect(result.profile.name).toBe('Test User');
    });

    it('should return existing session when valid session exists', async () => {
      const existingUser = {
        accessToken: 'existing-token',
        idToken: 'existing-id-token',
        expiresIn: 3600,
        profile: {
          sub: 'existing-user',
          name: 'Existing User',
          email: '<EMAIL>'
        }
      };

      // Mock getCurrentUser to return existing session
      const getCurrentUserSpy = jest.spyOn(AuthService as any, 'getCurrentUser')
        .mockResolvedValue(existingUser);

      // Mock signInForced to ensure it's NOT called
      const signInForcedSpy = jest.spyOn(AuthService, 'signInForced')
        .mockResolvedValue({} as any);

      // Call signIn
      const result = await AuthService.signIn();

      // Verify it checked for existing user
      expect(getCurrentUserSpy).toHaveBeenCalled();
      
      // Verify it did NOT call forced authentication
      expect(signInForcedSpy).not.toHaveBeenCalled();
      
      // Verify it returned the existing session
      expect(result).toBe(existingUser);
      expect(result.profile.name).toBe('Existing User');
    });
  });

  describe('signInForced() method', () => {
    it('should always force fresh authentication', async () => {
      // Mock clearBrowserSession
      const clearBrowserSessionSpy = jest.spyOn(AuthService as any, 'clearBrowserSession')
        .mockResolvedValue(undefined);

      // Mock other required methods
      jest.spyOn(AuthService as any, 'generateAuthState')
        .mockResolvedValue({
          state: 'test-state',
          codeVerifier: 'test-verifier',
          nonce: 'test-nonce',
          redirectUri: 'capacitor://localhost/callback',
          timestamp: Date.now()
        });

      jest.spyOn(AuthService as any, 'storeAuthState')
        .mockResolvedValue(undefined);

      jest.spyOn(AuthService as any, 'buildAuthorizationUrl')
        .mockResolvedValue('https://example.com/auth');

      // Mock InAppBrowser to avoid actual browser opening
      const { InAppBrowser } = require('@capacitor/inappbrowser');
      InAppBrowser.openInSystemBrowser.mockResolvedValue(undefined);

      // Call signInForced (but don't wait for completion as it would hang)
      const authPromise = AuthService.signInForced();

      // Give it a moment to start
      await new Promise(resolve => setTimeout(resolve, 10));

      // Verify it cleared browser session
      expect(clearBrowserSessionSpy).toHaveBeenCalled();

      // Verify it opened browser
      expect(InAppBrowser.openInSystemBrowser).toHaveBeenCalled();

      // Clean up the hanging promise
      (AuthService as any).cleanup();
    });
  });
});
