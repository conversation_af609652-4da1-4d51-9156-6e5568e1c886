import { Capacitor } from '@capacitor/core';
import { InA<PERSON><PERSON><PERSON>er, AndroidViewStyle, iOSViewStyle, iOSAnimation, AndroidAnimation, DismissStyle } from '@capacitor/inappbrowser';
import { App } from '@capacitor/app';
import { Preferences } from '@capacitor/preferences';
import { HttpService } from '../utils/http';
import { environmentConfig, debugLog } from '../config/environment.config';
import { CryptoUtils } from '../utils/crypto';

/**
 * OpenID Connect Service for Capacitor
 * Implements proper OIDC flow with Santillana Connect
 */

export interface OIDCTokens {
  access_token: string;
  id_token: string;
  refresh_token?: string;
  token_type: string;
  expires_in: number;
  scope: string;
}

export interface OIDCUserInfo {
  sub: string;
  name: string;
  email: string;
  email_verified: boolean;
  preferred_username?: string;
  given_name?: string;
  family_name?: string;
  picture?: string;
  [key: string]: any;
}

export interface OIDCAuthResult {
  tokens: OIDCTokens;
  userInfo: OIDCUserInfo;
  isAuthenticated: true;
}

interface OIDCState {
  state: string;
  nonce: string;
  codeVerifier: string;
  redirectUri: string;
  timestamp: number;
}

// Storage keys for OIDC data
const OIDC_STORAGE_KEYS = {
  ACCESS_TOKEN: 'oidc_access_token',
  ID_TOKEN: 'oidc_id_token',
  REFRESH_TOKEN: 'oidc_refresh_token',
  EXPIRES_AT: 'oidc_expires_at',
  USER_INFO: 'oidc_user_info',
  AUTH_STATE: 'oidc_auth_state'
} as const;

class OIDCLogger {
  static log(message: string, data?: any) {
    debugLog(`OIDC - ${message}`, data);
  }

  static error(message: string, error?: any) {
    console.error(`❌ [OIDC] ${message}`, error);
    if (environmentConfig.debugAuth) {
      console.error('Error details:', error);
    }
  }
}

export class OIDCCapacitorService {
  private static authInProgress = false;

  /**
   * Start OpenID Connect authentication flow
   */
  static async authenticate(): Promise<OIDCAuthResult> {
    if (this.authInProgress) {
      throw new Error('Authentication already in progress');
    }

    this.authInProgress = true;
    OIDCLogger.log('Starting OpenID Connect authentication flow');

    try {
      // Check if already authenticated
      const existingAuth = await this.getCurrentAuth();
      if (existingAuth) {
        OIDCLogger.log('User already authenticated, returning existing session');
        return existingAuth;
      }

      // Clear any existing session for fresh authentication
      await this.clearSession();

      // Generate OIDC state parameters
      const authState = await this.generateAuthState();
      await this.storeAuthState(authState);

      // Build authorization URL
      const authUrl = await this.buildAuthorizationUrl(authState);
      OIDCLogger.log('Opening OIDC authorization URL', {
        platform: Capacitor.getPlatform(),
        authority: environmentConfig.authority
      });

      // Perform authentication with InAppBrowser
      return await this.performAuthentication(authUrl, authState);

    } catch (error) {
      OIDCLogger.error('OIDC authentication failed', error);
      throw error;
    } finally {
      this.authInProgress = false;
    }
  }

  /**
   * Generate OIDC authentication state
   */
  private static async generateAuthState(): Promise<OIDCState> {
    const state = CryptoUtils.generateState(32);
    const nonce = CryptoUtils.generateNonce(32);
    const codeVerifier = CryptoUtils.generateCodeVerifier(128);
    const redirectUri = environmentConfig.redirectUri;

    const authState: OIDCState = {
      state,
      nonce,
      codeVerifier,
      redirectUri,
      timestamp: Date.now()
    };

    OIDCLogger.log('Generated OIDC auth state', {
      stateLength: state.length,
      nonceLength: nonce.length,
      codeVerifierLength: codeVerifier.length,
      redirectUri
    });

    return authState;
  }

  /**
   * Build OpenID Connect authorization URL
   */
  private static async buildAuthorizationUrl(authState: OIDCState): Promise<string> {
    // Santillana Connect REQUIRES PKCE (Code challenge required error)
    const usePKCE = true;

    let params: URLSearchParams;

    if (usePKCE) {
      const codeChallenge = await CryptoUtils.generateCodeChallenge(authState.codeVerifier);

      params = new URLSearchParams({
        response_type: 'code',
        client_id: environmentConfig.clientId,
        redirect_uri: authState.redirectUri,
        scope: environmentConfig.scope,
        state: authState.state,
        nonce: authState.nonce,
        code_challenge: codeChallenge,
        code_challenge_method: 'S256',
        // OIDC specific parameters
        response_mode: 'query',
        prompt: 'login' // Force fresh login for security
      });

      OIDCLogger.log('Using PKCE flow for OIDC authorization (required by Santillana Connect)');
    } else {
      // Authorization Code flow without PKCE (fallback - not used)
      params = new URLSearchParams({
        response_type: 'code',
        client_id: environmentConfig.clientId,
        redirect_uri: authState.redirectUri,
        scope: environmentConfig.scope,
        state: authState.state,
        nonce: authState.nonce,
        // OIDC specific parameters
        response_mode: 'query',
        prompt: 'login' // Force fresh login for security
      });

      OIDCLogger.log('Using Authorization Code flow WITHOUT PKCE (fallback)');
    }

    const authUrl = `${environmentConfig.authority}/connect/authorize?${params.toString()}`;

    OIDCLogger.log('Built OIDC authorization URL', {
      authority: environmentConfig.authority,
      clientId: environmentConfig.clientId,
      scope: environmentConfig.scope,
      responseType: 'code',
      responseMode: 'query',
      usePKCE
    });

    return authUrl;
  }

  /**
   * Perform authentication using InAppBrowser
   */
  private static async performAuthentication(authUrl: string, authState: OIDCState): Promise<OIDCAuthResult> {
    return new Promise((resolve, reject) => {
      const cleanup = () => {
        App.removeAllListeners();
      };

      // Set up deep link listener for OAuth callback
      App.addListener('appUrlOpen', async (event) => {
        try {
          OIDCLogger.log('Received OIDC callback', { callbackUrl: event.url });

          if (event.url.startsWith(authState.redirectUri)) {
            cleanup();
            
            // Process the callback
            const authResult = await this.processCallback(event.url, authState);
            resolve(authResult);
          }
        } catch (error) {
          cleanup();
          OIDCLogger.error('OIDC callback processing failed', error);
          reject(error);
        }
      });

      // Open authentication URL in InAppBrowser with proper options
      const browserOptions = {
        url: authUrl,
        options: {
          iOS: {
            closeButtonText: DismissStyle.CLOSE,
            viewStyle: iOSViewStyle.PAGE_SHEET,
            animationEffect: iOSAnimation.COVER_VERTICAL,
            enableBarsCollapsing: true,
            enableReadersMode: false
          },
          android: {
            showTitle: false,
            hideToolbarOnScroll: true,
            viewStyle: AndroidViewStyle.FULL_SCREEN,
            startAnimation: AndroidAnimation.SLIDE_IN_LEFT,
            exitAnimation: AndroidAnimation.SLIDE_OUT_RIGHT
          }
        }
      };

      InAppBrowser.openInSystemBrowser(browserOptions).then(() => {
        OIDCLogger.log('InAppBrowser opened successfully for OIDC authentication');
      }).catch((error) => {
        cleanup();
        OIDCLogger.error('Failed to open InAppBrowser', error);
        reject(error);
      });

      // Set timeout for authentication
      setTimeout(() => {
        cleanup();
        reject(new Error('Authentication timeout - user did not complete login'));
      }, 300000); // 5 minutes timeout
    });
  }

  /**
   * Process OAuth callback and exchange code for tokens
   */
  private static async processCallback(callbackUrl: string, authState: OIDCState): Promise<OIDCAuthResult> {
    const url = new URL(callbackUrl);
    const code = url.searchParams.get('code');
    const state = url.searchParams.get('state');
    const error = url.searchParams.get('error');

    // Check for errors
    if (error) {
      throw new Error(`OIDC authentication error: ${error}`);
    }

    // Validate required parameters
    if (!code || !state) {
      throw new Error('Missing required parameters in OIDC callback');
    }

    // Validate state parameter
    if (state !== authState.state) {
      throw new Error('Invalid state parameter - possible CSRF attack');
    }

    OIDCLogger.log('OIDC callback validated successfully', {
      hasCode: !!code,
      stateMatches: state === authState.state
    });

    // Clear auth state to prevent reuse
    await Preferences.remove({ key: OIDC_STORAGE_KEYS.AUTH_STATE });

    // Exchange code for tokens
    const tokens = await this.exchangeCodeForTokens(code, authState);

    // Get user info from ID token and UserInfo endpoint
    const userInfo = await this.getUserInfo(tokens);

    // Store tokens and user info
    await this.storeTokens(tokens);
    await this.storeUserInfo(userInfo);

    OIDCLogger.log('OIDC authentication completed successfully', {
      userId: userInfo.sub,
      email: userInfo.email,
      hasAccessToken: !!tokens.access_token,
      hasIdToken: !!tokens.id_token
    });

    return {
      tokens,
      userInfo,
      isAuthenticated: true
    };
  }

  /**
   * Exchange authorization code for tokens using OIDC token endpoint
   */
  private static async exchangeCodeForTokens(code: string, authState: OIDCState): Promise<OIDCTokens> {
    // Santillana Connect REQUIRES PKCE (Code challenge required error)
    const usePKCE = true;

    const tokenData = new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: environmentConfig.clientId,
      code,
      redirect_uri: authState.redirectUri
    });

    // Only add code_verifier if using PKCE
    if (usePKCE) {
      tokenData.append('code_verifier', authState.codeVerifier);
      OIDCLogger.log('Including code_verifier for PKCE flow (required by Santillana Connect)');
    } else {
      OIDCLogger.log('Omitting code_verifier - fallback mode');
    }

    OIDCLogger.log('Exchanging code for OIDC tokens', {
      endpoint: `${environmentConfig.authority}/connect/token`,
      grantType: 'authorization_code',
      clientId: environmentConfig.clientId,
      usePKCE
    });

    const response = await HttpService.post(
      `${environmentConfig.authority}/connect/token`,
      tokenData.toString(),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );

    if (!response.ok) {
      OIDCLogger.error('Token exchange failed', {
        status: response.status,
        data: response.data
      });
      throw new Error(`OIDC token exchange failed: ${response.status}`);
    }

    return response.data as OIDCTokens;
  }

  /**
   * Get user information from ID token and UserInfo endpoint
   */
  private static async getUserInfo(tokens: OIDCTokens): Promise<OIDCUserInfo> {
    // Parse ID token for basic user info
    const idTokenPayload = this.parseJwtPayload(tokens.id_token);
    
    try {
      // Get additional user info from UserInfo endpoint
      const userInfoResponse = await HttpService.get(
        `${environmentConfig.authority}/connect/userinfo`,
        {
          headers: {
            'Authorization': `Bearer ${tokens.access_token}`
          }
        }
      );

      if (userInfoResponse.ok) {
        // Merge ID token claims with UserInfo response
        return { ...idTokenPayload, ...userInfoResponse.data };
      }
    } catch (error) {
      OIDCLogger.error('Failed to fetch UserInfo, using ID token claims', error);
    }

    // Fallback to ID token claims only
    return idTokenPayload;
  }

  /**
   * Parse JWT payload (ID token)
   */
  private static parseJwtPayload(token: string): any {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new Error('Invalid JWT format');
      }

      const payload = parts[1];
      const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
      return JSON.parse(decoded);
    } catch (error) {
      throw new Error('Failed to parse ID token');
    }
  }

  /**
   * Store authentication state
   */
  private static async storeAuthState(authState: OIDCState): Promise<void> {
    await Preferences.set({
      key: OIDC_STORAGE_KEYS.AUTH_STATE,
      value: JSON.stringify(authState)
    });
  }

  /**
   * Store OIDC tokens
   */
  private static async storeTokens(tokens: OIDCTokens): Promise<void> {
    const expiresAt = Date.now() + (tokens.expires_in * 1000);

    await Promise.all([
      Preferences.set({ key: OIDC_STORAGE_KEYS.ACCESS_TOKEN, value: tokens.access_token }),
      Preferences.set({ key: OIDC_STORAGE_KEYS.ID_TOKEN, value: tokens.id_token }),
      Preferences.set({ key: OIDC_STORAGE_KEYS.EXPIRES_AT, value: expiresAt.toString() }),
      tokens.refresh_token ? Preferences.set({ key: OIDC_STORAGE_KEYS.REFRESH_TOKEN, value: tokens.refresh_token }) : Promise.resolve()
    ]);
  }

  /**
   * Store user information
   */
  private static async storeUserInfo(userInfo: OIDCUserInfo): Promise<void> {
    await Preferences.set({
      key: OIDC_STORAGE_KEYS.USER_INFO,
      value: JSON.stringify(userInfo)
    });
  }

  /**
   * Get current authentication state
   */
  static async getCurrentAuth(): Promise<OIDCAuthResult | null> {
    try {
      const [accessTokenResult, userInfoResult, expiresAtResult] = await Promise.all([
        Preferences.get({ key: OIDC_STORAGE_KEYS.ACCESS_TOKEN }),
        Preferences.get({ key: OIDC_STORAGE_KEYS.USER_INFO }),
        Preferences.get({ key: OIDC_STORAGE_KEYS.EXPIRES_AT })
      ]);

      if (!accessTokenResult.value || !userInfoResult.value) {
        return null;
      }

      const expiresAt = expiresAtResult.value ? parseInt(expiresAtResult.value) : 0;
      
      // Check if token is expired
      if (Date.now() >= expiresAt) {
        OIDCLogger.log('Stored tokens are expired');
        await this.signOut();
        return null;
      }

      const userInfo = JSON.parse(userInfoResult.value) as OIDCUserInfo;
      
      // Get all tokens
      const [idTokenResult, refreshTokenResult] = await Promise.all([
        Preferences.get({ key: OIDC_STORAGE_KEYS.ID_TOKEN }),
        Preferences.get({ key: OIDC_STORAGE_KEYS.REFRESH_TOKEN })
      ]);

      const tokens: OIDCTokens = {
        access_token: accessTokenResult.value,
        id_token: idTokenResult.value || '',
        refresh_token: refreshTokenResult.value || undefined,
        token_type: 'Bearer',
        expires_in: Math.floor((expiresAt - Date.now()) / 1000),
        scope: environmentConfig.scope
      };

      return {
        tokens,
        userInfo,
        isAuthenticated: true
      };

    } catch (error) {
      OIDCLogger.error('Failed to get current auth state', error);
      return null;
    }
  }

  /**
   * Sign out and clear all stored data
   */
  static async signOut(): Promise<void> {
    try {
      OIDCLogger.log('Starting OIDC sign out');

      await this.clearSession();
      
      OIDCLogger.log('OIDC sign out completed');
    } catch (error) {
      OIDCLogger.error('Error during OIDC sign out', error);
      throw error;
    }
  }

  /**
   * Clear all stored session data
   */
  private static async clearSession(): Promise<void> {
    await Promise.all([
      Preferences.remove({ key: OIDC_STORAGE_KEYS.ACCESS_TOKEN }),
      Preferences.remove({ key: OIDC_STORAGE_KEYS.ID_TOKEN }),
      Preferences.remove({ key: OIDC_STORAGE_KEYS.REFRESH_TOKEN }),
      Preferences.remove({ key: OIDC_STORAGE_KEYS.EXPIRES_AT }),
      Preferences.remove({ key: OIDC_STORAGE_KEYS.USER_INFO }),
      Preferences.remove({ key: OIDC_STORAGE_KEYS.AUTH_STATE })
    ]);

    OIDCLogger.log('OIDC session data cleared');
  }

  /**
   * Refresh access token using refresh token
   */
  static async refreshToken(): Promise<OIDCTokens | null> {
    try {
      const refreshTokenResult = await Preferences.get({ key: OIDC_STORAGE_KEYS.REFRESH_TOKEN });
      
      if (!refreshTokenResult.value) {
        OIDCLogger.log('No refresh token available');
        return null;
      }

      const tokenData = new URLSearchParams({
        grant_type: 'refresh_token',
        client_id: environmentConfig.clientId,
        refresh_token: refreshTokenResult.value
      });

      const response = await HttpService.post(
        `${environmentConfig.authority}/connect/token`,
        tokenData.toString(),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      if (!response.ok) {
        OIDCLogger.error('Token refresh failed', response);
        await this.signOut();
        return null;
      }

      const tokens = response.data as OIDCTokens;
      await this.storeTokens(tokens);
      
      OIDCLogger.log('Tokens refreshed successfully');
      return tokens;

    } catch (error) {
      OIDCLogger.error('Token refresh error', error);
      await this.signOut();
      return null;
    }
  }
}
