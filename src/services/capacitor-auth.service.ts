/**
 * Capacitor-Native Authentication Service
 * Streamlined OAuth2 authentication for Santillana Connect using InAppBrowser
 * Optimized for iOS and Android platforms with seamless user experience
 */

import { Capacitor } from '@capacitor/core';
import { InA<PERSON><PERSON>rowser, AndroidViewStyle, iOSViewStyle, iOSAnimation, AndroidAnimation, DismissStyle } from '@capacitor/inappbrowser';
import { App } from '@capacitor/app';
import { Preferences } from '@capacitor/preferences';
import { environmentConfig, debugLog } from '../config/environment.config';
import { CryptoUtils } from '../utils/crypto';
import { HttpService } from '../utils/http';

// Types and Interfaces
export interface AuthTokens {
  access_token: string;
  id_token?: string;
  refresh_token?: string;
  expires_in: number;
  token_type: string;
}

export interface UserProfile {
  sub: string;
  name: string;
  email: string;
  picture?: string;
  [key: string]: any;
}

export interface AuthResult {
  accessToken: string;
  idToken?: string;
  refreshToken?: string;
  expiresIn: number;
  profile: UserProfile;
}

interface AuthState {
  state: string;
  codeVerifier: string;
  nonce: string;
  redirectUri: string;
  timestamp: number;
}

// Storage keys for authentication data
const AUTH_STORAGE_KEYS = {
  ACCESS_TOKEN: 'capacitor_auth_access_token',
  ID_TOKEN: 'capacitor_auth_id_token',
  REFRESH_TOKEN: 'capacitor_auth_refresh_token',
  EXPIRES_AT: 'capacitor_auth_expires_at',
  USER_PROFILE: 'capacitor_auth_user_profile',
  AUTH_STATE: 'capacitor_auth_state'
};

// Logger utility
class AuthLogger {
  static log(message: string, data?: any) {
    debugLog(`CapacitorAuth - ${message}`, data);
  }

  static error(message: string, error?: any) {
    console.error(`❌ [AUTH] CapacitorAuth - ${message}`, error);
    if (environmentConfig.debugAuth) {
      console.error('Error details:', error);
    }
  }
}

/**
 * Capacitor-Native Authentication Service
 * Streamlined OAuth2 PKCE flow with InAppBrowser for optimal mobile UX
 */
export class CapacitorAuthService {
  private static readonly TOKEN_KEYS = AUTH_STORAGE_KEYS;
  private static authInProgress = false;

  /**
   * Start OAuth2 authentication flow using InAppBrowser
   */
  static async signIn(): Promise<AuthResult> {
    if (this.authInProgress) {
      throw new Error('Authentication already in progress');
    }

    try {
      this.authInProgress = true;
      AuthLogger.log('Starting Capacitor-native authentication flow');

      // Verify we're on a native platform
      if (!Capacitor.isNativePlatform()) {
        throw new Error('CapacitorAuthService is designed for native platforms only');
      }

      // Check for existing valid authentication
      const existingAuth = await this.getCurrentUser();
      if (existingAuth) {
        AuthLogger.log('Using existing valid authentication');
        return existingAuth;
      }

      // Clear any cached browser session for fresh authentication
      await this.clearBrowserSession();

      // Generate PKCE parameters and state
      const authState = await this.generateAuthState();
      await this.storeAuthState(authState);

      const authUrl = await this.buildAuthUrl(authState);
      AuthLogger.log('Opening authentication URL in InAppBrowser', {
        platform: Capacitor.getPlatform(),
        authUrl: authUrl.substring(0, 100) + '...' // Log truncated URL for security
      });

      return await this.authenticateWithInAppBrowser(authUrl, authState);
    } catch (error) {
      AuthLogger.error('Authentication failed', error);
      throw error;
    } finally {
      this.authInProgress = false;
    }
  }

  /**
   * Authenticate using InAppBrowser with seamless UX
   */
  private static async authenticateWithInAppBrowser(authUrl: string, authState: AuthState): Promise<AuthResult> {
    return new Promise(async (resolve, reject) => {
      let removeListener: any = null;
      let timeoutId: NodeJS.Timeout | null = null;

      try {
        // Set up deep link listener for callback
        removeListener = await App.addListener('appUrlOpen', async (event) => {
          try {
            // Clean up listener and timeout
            if (removeListener) {
              await removeListener.remove();
              removeListener = null;
            }
            if (timeoutId) {
              clearTimeout(timeoutId);
              timeoutId = null;
            }

            const result = await this.handleAuthCallback(event.url, authState);
            resolve(result);
          } catch (error) {
            reject(error);
          }
        });

        // Open InAppBrowser with optimized options for seamless UX
        const browserOptions = {
          url: authUrl,
          options: {
            iOS: {
              closeButtonText: DismissStyle.CLOSE,
              viewStyle: iOSViewStyle.PAGE_SHEET,
              animationEffect: iOSAnimation.COVER_VERTICAL,
              enableBarsCollapsing: true,
              enableReadersMode: false
            },
            android: {
              showTitle: false,
              hideToolbarOnScroll: true,
              viewStyle: AndroidViewStyle.FULL_SCREEN,
              startAnimation: AndroidAnimation.SLIDE_IN_LEFT,
              exitAnimation: AndroidAnimation.SLIDE_OUT_RIGHT
            }
          }
        };

        await InAppBrowser.openInSystemBrowser(browserOptions);

        // Timeout after 5 minutes
        timeoutId = setTimeout(async () => {
          if (removeListener) {
            await removeListener.remove();
            removeListener = null;
          }
          reject(new Error('Authentication timeout'));
        }, 300000);

      } catch (error) {
        // Clean up on error
        if (removeListener) {
          await removeListener.remove();
        }
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        AuthLogger.error('InAppBrowser authentication failed', error);
        reject(error);
      }
    });
  }

  /**
   * Handle OAuth2 callback from deep link
   */
  private static async handleAuthCallback(callbackUrl: string, authState: AuthState): Promise<AuthResult> {
    try {
      AuthLogger.log('Processing OAuth2 callback', { callbackUrl });

      const url = new URL(callbackUrl);
      const code = url.searchParams.get('code');
      const state = url.searchParams.get('state');
      const error = url.searchParams.get('error');

      // Enhanced debugging for callback parameters
      AuthLogger.log('Callback parameters extracted', {
        hasCode: !!code,
        hasState: !!state,
        hasError: !!error,
        codeLength: code?.length || 0,
        stateMatches: state === authState.state,
        expectedState: authState.state,
        receivedState: state,
        error: error
      });

      if (error) {
        throw new Error(`OAuth2 error: ${error}`);
      }

      if (!code || !state) {
        throw new Error('Missing authorization code or state parameter');
      }

      if (state !== authState.state) {
        throw new Error('Invalid state parameter - possible CSRF attack');
      }

      // Check if auth state is too old (prevent replay attacks and expired codes)
      const authAge = Date.now() - authState.timestamp;
      const maxAge = 10 * 60 * 1000; // 10 minutes
      if (authAge > maxAge) {
        AuthLogger.error('Authorization state expired', {
          authAge: authAge,
          maxAge: maxAge,
          authTimestamp: authState.timestamp,
          currentTime: Date.now()
        });
        throw new Error('Authorization state expired - please try again');
      }

      // Add a small delay before token exchange to prevent timing issues
      AuthLogger.log('Adding small delay before token exchange to prevent timing issues');
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Clear the auth state immediately to prevent reuse
      await Preferences.remove({ key: this.TOKEN_KEYS.AUTH_STATE });

      // Exchange authorization code for tokens
      const tokenResponse = await this.exchangeCodeForTokens(code, authState);

      // Parse ID token to get user profile
      if (!tokenResponse.id_token) {
        throw new Error('No ID token received from authorization server');
      }
      const profile = this.parseJwtPayload(tokenResponse.id_token);

      // Create auth result
      const authResult: AuthResult = {
        accessToken: tokenResponse.access_token,
        idToken: tokenResponse.id_token,
        refreshToken: tokenResponse.refresh_token,
        expiresIn: tokenResponse.expires_in || 3600,
        profile: {
          sub: profile.sub,
          name: profile.name,
          email: profile.email,
          ...profile
        }
      };

      // Store authentication data
      await this.storeAuthResult(authResult);

      AuthLogger.log('Authentication successful', { 
        userId: profile.sub,
        userName: profile.name 
      });

      return authResult;
    } catch (error) {
      AuthLogger.error('Callback handling failed', error);
      throw error;
    }
  }

  /**
   * Generate authentication state with PKCE parameters
   */
  private static async generateAuthState(): Promise<AuthState> {
    try {
      // Use CryptoUtils for better cross-platform compatibility
      const state = CryptoUtils.generateState(32);
      const codeVerifier = CryptoUtils.generateCodeVerifier(128);
      const nonce = CryptoUtils.generateNonce(32);

      // Debug logging for generated values
      AuthLogger.log('Generated authentication state', {
        stateLength: state.length,
        codeVerifierLength: codeVerifier.length,
        nonceLength: nonce.length,
        redirectUri: environmentConfig.redirectUri,
        timestamp: Date.now()
      });

      return {
        state,
        codeVerifier,
        nonce,
        redirectUri: environmentConfig.redirectUri,
        timestamp: Date.now()
      };
    } catch (error) {
      AuthLogger.error('Failed to generate authentication state', error);
      throw new Error(`Authentication state generation failed: ${error}`);
    }
  }

  /**
   * Build OAuth2 authorization URL with PKCE
   */
  private static async buildAuthUrl(authState: AuthState): Promise<string> {
    try {
      const codeChallenge = await CryptoUtils.generateCodeChallenge(authState.codeVerifier);

      // Debug PKCE parameters
      AuthLogger.log('PKCE parameters generated', {
        codeChallengeLength: codeChallenge.length,
        codeVerifierLength: authState.codeVerifier.length,
        codeChallengePrefix: codeChallenge.substring(0, 10) + '...',
        codeVerifierPrefix: authState.codeVerifier.substring(0, 10) + '...'
      });

      const params = new URLSearchParams({
        response_type: 'code',
        client_id: environmentConfig.clientId,
        redirect_uri: authState.redirectUri,
        scope: environmentConfig.scope,
        state: authState.state,
        nonce: authState.nonce,
        code_challenge: codeChallenge,
        code_challenge_method: 'S256'
      });

      const authUrl = `${environmentConfig.authority}/connect/authorize?${params.toString()}`;

      // Debug authorization URL parameters
      AuthLogger.log('Authorization URL built', {
        authority: environmentConfig.authority,
        clientId: environmentConfig.clientId,
        redirectUri: authState.redirectUri,
        scope: environmentConfig.scope,
        responseType: 'code',
        codeChallengeMethod: 'S256'
      });

      return authUrl;
    } catch (error) {
      AuthLogger.error('Failed to build authorization URL', error);
      throw error;
    }
  }

  /**
   * Exchange authorization code for tokens
   */
  private static async exchangeCodeForTokens(code: string, authState: AuthState): Promise<AuthTokens> {
    try {
      const tokenData = new URLSearchParams({
        grant_type: 'authorization_code',
        client_id: environmentConfig.clientId,
        code,
        redirect_uri: authState.redirectUri,
        code_verifier: authState.codeVerifier
      });

      // Debug logging for token exchange
      AuthLogger.log('Token exchange request details', {
        endpoint: `${environmentConfig.authority}/connect/token`,
        clientId: environmentConfig.clientId,
        redirectUri: authState.redirectUri,
        codeLength: code.length,
        codeVerifierLength: authState.codeVerifier.length,
        // Don't log actual sensitive values
        codePrefix: code.substring(0, 10) + '...',
        codeVerifierPrefix: authState.codeVerifier.substring(0, 10) + '...'
      });

      // Log the exact form data being sent (without sensitive values)
      AuthLogger.log('Token exchange form data structure', {
        grant_type: 'authorization_code',
        client_id: environmentConfig.clientId,
        redirect_uri: authState.redirectUri,
        code_present: !!code,
        code_verifier_present: !!authState.codeVerifier,
        form_data_length: tokenData.toString().length
      });

      // Verify PKCE parameters match what was used in authorization
      const originalChallenge = await CryptoUtils.generateCodeChallenge(authState.codeVerifier);
      AuthLogger.log('PKCE verification for token exchange', {
        codeVerifierLength: authState.codeVerifier.length,
        regeneratedChallengeLength: originalChallenge.length,
        challengeMatches: 'Will be verified by server',
        codeVerifierPrefix: authState.codeVerifier.substring(0, 10) + '...',
        regeneratedChallengePrefix: originalChallenge.substring(0, 10) + '...'
      });

      const response = await HttpService.post(
        `${environmentConfig.authority}/connect/token`,
        tokenData.toString(),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      if (!response.ok) {
        // Enhanced error logging for debugging
        AuthLogger.error('Token exchange failed with detailed info', {
          status: response.status,
          responseData: response.data,
          requestData: {
            grant_type: 'authorization_code',
            client_id: environmentConfig.clientId,
            redirect_uri: authState.redirectUri,
            code_length: code.length,
            code_verifier_length: authState.codeVerifier.length
          }
        });

        // Check if this is a redirect URI issue
        if (response.data?.error === 'invalid_grant') {
          AuthLogger.error('INVALID_GRANT error - possible causes:', {
            'Redirect URI mismatch': 'Server expects different redirect_uri than capacitor://localhost/callback',
            'Client not configured for PKCE': 'OAuth2 client may not support PKCE flow',
            'Authorization code expired': 'Code may have expired (usually 10 minutes)',
            'Authorization code already used': 'Code can only be used once',
            'Client configuration': 'Client may not be configured for mobile/native apps'
          });
        }

        throw new Error(`Token exchange failed: ${response.status}`);
      }

      return response.data as AuthTokens;
    } catch (error) {
      AuthLogger.error('Token exchange failed', error);
      throw error;
    }
  }

  /**
   * Get current authenticated user
   */
  static async getCurrentUser(): Promise<AuthResult | null> {
    try {
      const result = await Preferences.get({ key: this.TOKEN_KEYS.ACCESS_TOKEN });
      if (!result.value) {
        return null;
      }

      const expiresAtResult = await Preferences.get({ key: this.TOKEN_KEYS.EXPIRES_AT });
      const expiresAt = expiresAtResult.value ? parseInt(expiresAtResult.value) : 0;

      // Check if token is expired
      if (Date.now() >= expiresAt) {
        AuthLogger.log('Stored token is expired');
        await this.signOut();
        return null;
      }

      const profileResult = await Preferences.get({ key: this.TOKEN_KEYS.USER_PROFILE });
      const profile = profileResult.value ? JSON.parse(profileResult.value) : null;

      if (!profile) {
        return null;
      }

      return {
        accessToken: result.value,
        profile,
        expiresIn: Math.floor((expiresAt - Date.now()) / 1000)
      } as AuthResult;
    } catch (error) {
      AuthLogger.error('Failed to get current user', error);
      return null;
    }
  }

  /**
   * Sign out user and clear all authentication data
   */
  static async signOut(): Promise<void> {
    try {
      AuthLogger.log('Starting sign out');
      
      await this.clearAuthData();
      await this.clearBrowserSession();
      
      AuthLogger.log('Sign out completed');
    } catch (error) {
      AuthLogger.error('Sign out failed', error);
      throw error;
    }
  }

  /**
   * Check if user is authenticated
   */
  static async isAuthenticated(): Promise<boolean> {
    const user = await this.getCurrentUser();
    return user !== null;
  }

  /**
   * Store authentication result
   */
  private static async storeAuthResult(authResult: AuthResult): Promise<void> {
    try {
      const expiresAt = Date.now() + (authResult.expiresIn * 1000);

      await Promise.all([
        Preferences.set({ key: this.TOKEN_KEYS.ACCESS_TOKEN, value: authResult.accessToken }),
        Preferences.set({ key: this.TOKEN_KEYS.ID_TOKEN, value: authResult.idToken || '' }),
        Preferences.set({ key: this.TOKEN_KEYS.REFRESH_TOKEN, value: authResult.refreshToken || '' }),
        Preferences.set({ key: this.TOKEN_KEYS.EXPIRES_AT, value: expiresAt.toString() }),
        Preferences.set({ key: this.TOKEN_KEYS.USER_PROFILE, value: JSON.stringify(authResult.profile) })
      ]);

      AuthLogger.log('Authentication data stored successfully');
    } catch (error) {
      AuthLogger.error('Failed to store authentication data', error);
      throw error;
    }
  }

  /**
   * Store authentication state
   */
  private static async storeAuthState(authState: AuthState): Promise<void> {
    try {
      await Preferences.set({
        key: this.TOKEN_KEYS.AUTH_STATE,
        value: JSON.stringify(authState)
      });
    } catch (error) {
      AuthLogger.error('Failed to store authentication state', error);
      throw error;
    }
  }

  /**
   * Clear all authentication data
   */
  private static async clearAuthData(): Promise<void> {
    try {
      await Promise.all([
        Preferences.remove({ key: this.TOKEN_KEYS.ACCESS_TOKEN }),
        Preferences.remove({ key: this.TOKEN_KEYS.ID_TOKEN }),
        Preferences.remove({ key: this.TOKEN_KEYS.REFRESH_TOKEN }),
        Preferences.remove({ key: this.TOKEN_KEYS.EXPIRES_AT }),
        Preferences.remove({ key: this.TOKEN_KEYS.USER_PROFILE }),
        Preferences.remove({ key: this.TOKEN_KEYS.AUTH_STATE })
      ]);

      AuthLogger.log('Authentication data cleared');
    } catch (error) {
      AuthLogger.error('Failed to clear authentication data', error);
      throw error;
    }
  }

  /**
   * Clear browser session to ensure fresh authentication
   * Prevents cached credential issues mentioned in user preferences
   */
  private static async clearBrowserSession(): Promise<void> {
    try {
      // Platform-specific session clearing for better security
      const platform = Capacitor.getPlatform();

      if (platform === 'ios' || platform === 'android') {
        AuthLogger.log(`Clearing browser session for ${platform} platform to prevent credential caching`);

        // Additional cleanup for native platforms to ensure session isolation
        // This addresses the security concern about InAppBrowser caching credentials
        await Preferences.remove({ key: AUTH_STORAGE_KEYS.AUTH_STATE });
      }

      AuthLogger.log('Browser session cleared for fresh authentication with session isolation');
    } catch (error) {
      AuthLogger.error('Failed to clear browser session', error);
    }
  }

  /**
   * Parse JWT payload
   */
  private static parseJwtPayload(token: string): any {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      AuthLogger.error('Failed to parse JWT payload', error);
      throw new Error('Invalid JWT token');
    }
  }


}
