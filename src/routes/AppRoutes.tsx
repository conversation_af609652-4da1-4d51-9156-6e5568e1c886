import React, { useEffect, useState } from "react";
import { IonRouterOutlet, IonTabs, IonTabBar, IonTabButton, IonIcon, IonLabel, IonPage, IonContent } from '@ionic/react';
import { home as homeIcon, logOut as logoutIcon, square, triangle } from 'ionicons/icons';
import { Route, Redirect, useHistory, useLocation } from "react-router-dom";
import RouteGuard from "./RouteGuard";
// Removed old OIDC imports - now using Capacitor-native authentication
import { environmentConfig, debugLog } from "../config/environment.config";
// OIDC debug utilities removed as part of simplification
import { ROUTES } from './routes';
import WelcomeSlides from '../pages/WelcomeSlides/WelcomeSlides';
import CapacitorAuthPage from '../pages/Auth/CapacitorAuthPage';
import Tab1 from "../pages/HomePage/Tab1";
import Tab2 from "../pages/HomePage/Tab2";
import Tab3 from "../pages/HomePage/Tab3";
import CatalogoTab from "../pages/HomePage/CatalogoTab";
import AccountPage from "../pages/AccountPage/AccountPage";
import AuthErrorBoundary from '../components/ErrorBoundary/AuthErrorBoundary';
import AuthDebugPage from '../pages/Debug/AuthDebugPage';

// Removed old CallbackPage - now using Capacitor-native deep link handling

// Silent refresh page for token renewal
const SilentRefreshPage: React.FC = () => {
  useEffect(() => {
    debugLog("SilentRefreshPage - Processing silent refresh");
  }, []);

  return (
    <IonPage>
      <IonContent>
        <div style={{ display: 'none' }}>
          Renovando tokens de forma silenciosa...
        </div>
      </IonContent>
    </IonPage>
  );
};

// Removed old LogoutPage - now using Capacitor-native logout

const AppRoutes: React.FC = () => {
  return (
    <IonRouterOutlet>
      {/* Rutas públicas */}
      <Route exact path={ROUTES.WELCOME} component={WelcomeSlides} />

      {/* Authentication routes wrapped with error boundary */}
      <Route exact path={ROUTES.AUTH}>
        <AuthErrorBoundary>
          <CapacitorAuthPage />
        </AuthErrorBoundary>
      </Route>

      {/* Removed old /callback route - now using Capacitor-native deep link handling */}

      {/** Silent refresh route (for token renewal) */}
      <Route exact path="/silent-refresh" component={SilentRefreshPage} />

      {/** Removed old logout route - now using Capacitor-native logout */}

      {/** Debug route for testing authentication (optional - only for development) */}
      {process.env.NODE_ENV === 'development' && (
        <Route exact path="/debug" component={AuthDebugPage} />
      )}

      {/* Protected routes - all require OIDC authentication */}
      <Route path={ROUTES.HOME}>
        <RouteGuard>
          <IonTabs>
            <IonRouterOutlet>
              {/* Tab sub-routes */}
              <Route exact path={ROUTES.TABS.HOME} component={Tab1} />
              <Route exact path={ROUTES.TABS.REPORTS} component={Tab2} />
              <Route exact path={ROUTES.TABS.CONNECTION} component={Tab3} />
              <Route exact path={ROUTES.TABS.RESOURCES} component={Tab3} />
              <Route exact path={ROUTES.TABS.CATALOG} component={CatalogoTab} />
              <Route exact path={ROUTES.TABS.ACCOUNT} component={AccountPage} />

              {/* Default tab redirect */}
              <Route exact path={ROUTES.HOME}>
                <Redirect to={ROUTES.TABS.HOME} />
              </Route>
            </IonRouterOutlet>

            {/* Tab Bar */}
            <IonTabBar slot="bottom">
              <IonTabButton tab="inicio" href={ROUTES.TABS.HOME}>
                <IonIcon icon={homeIcon} />
                <IonLabel>Inicio</IonLabel>
              </IonTabButton>

              <IonTabButton tab="informes" href={ROUTES.TABS.REPORTS}>
                <IonIcon icon={square} />
                <IonLabel>Informes</IonLabel>
              </IonTabButton>

              <IonTabButton tab="conexion" href={ROUTES.TABS.CONNECTION}>
                <IonIcon icon={triangle} />
                <IonLabel>Conexión</IonLabel>
              </IonTabButton>

              <IonTabButton tab="recursos" href={ROUTES.TABS.RESOURCES}>
                <IonIcon icon={triangle} />
                <IonLabel>Recursos</IonLabel>
              </IonTabButton>

              <IonTabButton tab="catalogo" href={ROUTES.TABS.CATALOG}>
                <IonIcon icon={square} />
                <IonLabel>Catálogo</IonLabel>
              </IonTabButton>

              <IonTabButton tab="cuenta" href={ROUTES.TABS.ACCOUNT}>
                <IonIcon icon={homeIcon} />
                <IonLabel>Cuenta</IonLabel>
              </IonTabButton>
            </IonTabBar>
          </IonTabs>
        </RouteGuard>
      </Route>

      {/* Ruta por defecto */}
      <Route exact path="/">
        <Redirect to={ROUTES.HOME} />
      </Route>
    </IonRouterOutlet>
  );
};

export default AppRoutes;
